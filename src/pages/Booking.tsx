
import React, { useState } from 'react';
import { ArrowLef<PERSON>, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { FormProvider } from '../contexts/FormContext';
import StepOne from '../components/booking/StepOne';
import StepTwo from '../components/booking/StepTwo';
import StepThree from '../components/booking/StepThree';
import StepFour from '../components/booking/StepFour';
import BookingInfo from '../components/booking/BookingInfo';
import { useFormContext } from '../contexts/FormContext';
import { Progress } from '../components/ui/progress';
import { toast } from '../hooks/use-toast';
import { redirectToHumainMarketing } from '../services/redirectionService';
// TODO: Migrate to Neon database
// import { sql } from '../integrations/neon/client';

// StepSelector component to render the current step
const StepSelector = () => {
  const { currentStep } = useFormContext();

  switch (currentStep) {
    case 1:
      return <StepOne />;
    case 2:
      return <StepTwo />;
    case 3:
      return <StepThree />;
    case 4:
      return <StepFour />;
    default:
      return <StepOne />;
  }
};

// Navigation buttons component
const NavigationButtons = () => {
  const { goToNextStep, goToPreviousStep, currentStep, validateCurrentStep, formData, leadId } = useFormContext();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (validateCurrentStep()) {
      setIsSubmitting(true);
      
      try {
        toast({
          title: "Your consultation is being booked!",
          description: "Connecting you to the booking platform...",
        });
        
        // TODO: Migrate to Neon database
        // Update lead as completed in database
        if (leadId) {
          console.log('TODO: Update lead as completed in Neon database:', leadId);
          // await sql`
          //   UPDATE leads
          //   SET is_completed = true, converted_to_customer = true
          //   WHERE id = ${leadId}
          // `;
        }
        
        // Prepare client data for humain.marketing
        const clientData = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          companyName: formData.companyName,
          website: formData.website,
          businessType: formData.businessType,
          fundingStatus: formData.fundingStatus,
          businessStage: formData.businessStage, 
          industry: formData.industry === 'other' ? formData.otherIndustry : formData.industry,
          runningAds: formData.runningAds,
          adsEffective: formData.adsEffective,
          adBudget: formData.adBudget,
          marketingManagement: formData.marketingManagement,
          marketingAreas: formData.marketingAreas,
          decisionMakers: formData.decisionMakers,
          implementationTimeline: formData.implementationTimeline,
          marketingChallenges: formData.marketingChallenges,
          sessionOutcomes: formData.sessionOutcomes
        };
        
        // Clear local storage lead ID after successful submission
        localStorage.removeItem('leadId');
        
        // Redirect to humain.marketing
        await redirectToHumainMarketing(clientData);
      } catch (error) {
        console.error('Error during submission:', error);
        toast({
          title: "Oops! Something went wrong",
          description: "Please try again or contact our support team.",
          variant: "destructive"
        });
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div className="flex justify-between mt-8">
      {currentStep > 1 ? (
        <button
          type="button"
          onClick={goToPreviousStep}
          className="neo-button bg-white hover:bg-muted flex items-center gap-2"
          disabled={isSubmitting}
        >
          <ArrowLeft size={18} />
          Back
        </button>
      ) : (
        <button
          type="button"
          onClick={() => navigate('/')}
          className="neo-button bg-white hover:bg-muted flex items-center gap-2"
          disabled={isSubmitting}
        >
          <ArrowLeft size={18} />
          Back to Home
        </button>
      )}

      <button
        type="button"
        onClick={currentStep < 4 ? goToNextStep : handleSubmit}
        className="neo-button bg-primary hover:bg-primary/90 flex items-center gap-2"
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <>
            <span className="animate-pulse">Booking your consultation...</span>
          </>
        ) : currentStep < 4 ? (
          <>
            Next
            <ArrowRight size={18} />
          </>
        ) : (
          'Book Your Consultation'
        )}
      </button>
    </div>
  );
};

// BookingPage wrapper component
const BookingPage = () => {
  return (
    <FormProvider>
      <div className="min-h-screen flex flex-col">
        <header className="bg-white neo-border border-l-0 border-r-0 border-t-0 py-4">
          <div className="container mx-auto px-4">
            <h1 className="text-2xl font-bold">
              <a href="/" className="hover:text-primary transition-colors">
                Asger.me
              </a>
            </h1>
          </div>
        </header>

        <BookingContent />
      </div>
    </FormProvider>
  );
};

// Main content component
const BookingContent = () => {
  const { currentStep } = useFormContext();
  
  const progressValue = (currentStep / 4) * 100;
  
  return (
    <main className="flex-grow py-8 md:py-12">
      <div className="container mx-auto px-4">
        <div className="w-full mb-6">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium">Step {currentStep} of 4</p>
            <p className="text-sm font-medium">{progressValue}% Complete</p>
          </div>
          <Progress value={progressValue} className="neo-border h-4" />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Column */}
          <div>
            <div className="neo-card p-6 md:p-8">
              <StepSelector />
              <NavigationButtons />
            </div>
          </div>
          
          {/* Info Column */}
          <div className="neo-card p-6 md:p-8">
            <BookingInfo />
          </div>
        </div>
      </div>
    </main>
  );
};

export default BookingPage;
