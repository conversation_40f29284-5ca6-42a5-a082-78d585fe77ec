
import React from 'react';

interface HeroProps {
  onBookingClick: () => void;
}

const Hero: React.FC<HeroProps> = ({ onBookingClick }) => {
  return (
    <section className="pt-32 pb-20 md:pt-40 md:pb-28 overflow-hidden">
      <div className="container mx-auto px-4 relative">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="relative z-10">
            <div className="animate-slide-in [animation-delay:0.1s] opacity-0">
              <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
                Marketing Expert
              </span>
            </div>

            <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6 animate-slide-in [animation-delay:0.2s] opacity-0">
              Strategic Marketing <span className="relative inline-block">
                Consulting
                <svg className="absolute -bottom-2 left-0 w-full h-3 text-primary" viewBox="0 0 100 8" preserveAspectRatio="none">
                  <path d="M0,5 Q40,0 50,5 Q60,10 100,5" stroke="currentColor" strokeWidth="4" fill="none" />
                </svg>
              </span>
            </h1>

            <p className="text-xl mb-8 animate-slide-in [animation-delay:0.3s] opacity-0">
              Transform your marketing strategy with expert guidance. Get actionable insights
              from a seasoned marketing professional who understands the challenges of
              scaling businesses and driving sustainable growth.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 animate-slide-in [animation-delay:0.4s] opacity-0">
              <button
                onClick={onBookingClick}
                className="neo-button bg-primary hover:bg-primary/90 text-black text-lg"
              >
                Book Strategy Call
              </button>

              <a
                href="#services"
                className="neo-button bg-white hover:bg-muted text-black text-lg"
              >
                View Services
              </a>
            </div>
          </div>
          
          <div className="relative hidden lg:block">
            <div className="absolute -top-10 -left-10 w-40 h-40 bg-yellow neo-border rotate-12 animate-float"></div>
            <div className="absolute top-20 -right-5 w-24 h-24 bg-pink neo-border -rotate-6 animate-float [animation-delay:1s]"></div>
            <div className="absolute -bottom-10 left-20 w-32 h-32 bg-blue neo-border rotate-3 animate-float [animation-delay:2s]"></div>
            
            <div className="neo-card overflow-hidden bg-white p-6 rotate-3 animate-fade-in max-h-[500px]">
              <img 
                src="/nerd.jpeg" 
                alt="Marketing professionals collaborating" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
