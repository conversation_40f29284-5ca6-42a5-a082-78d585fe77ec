import { usePostHog } from 'posthog-js/react';

// Event types for type safety
export interface AnalyticsEvents {
  // Page views
  'page_viewed': {
    page_name: string;
    path: string;
    referrer?: string;
  };

  // Booking form events
  'booking_form_started': {
    source?: string;
  };
  'booking_form_step_completed': {
    step: number;
    step_name: string;
  };
  'booking_form_step_abandoned': {
    step: number;
    step_name: string;
    time_spent_seconds?: number;
  };
  'booking_form_completed': {
    total_time_seconds?: number;
    lead_id?: string;
  };
  'booking_form_error': {
    step: number;
    error_type: string;
    error_message?: string;
  };

  // Contact form events
  'contact_form_started': {
    source?: string;
  };
  'contact_form_submitted': {
    has_company: boolean;
    message_length: number;
  };
  'contact_form_error': {
    error_type: string;
    error_message?: string;
  };

  // Service checkout events
  'service_viewed': {
    service_id: string;
    service_name: string;
    service_price?: number;
  };
  'service_checkout_started': {
    service_id: string;
    service_name: string;
    service_price?: number;
  };
  'service_checkout_completed': {
    service_id: string;
    service_name: string;
    service_price?: number;
    payment_method?: string;
  };
  'service_checkout_abandoned': {
    service_id: string;
    service_name: string;
    step: string;
  };

  // User interaction events
  'cta_clicked': {
    cta_text: string;
    cta_location: string;
    destination: string;
  };
  'navigation_clicked': {
    link_text: string;
    destination: string;
    source_page: string;
  };
  'website_analyzed': {
    website_url: string;
    analysis_successful: boolean;
    company_detected?: string;
    industry_detected?: string;
  };
  'external_redirect': {
    destination: string;
    source_page: string;
    context?: string;
  };

  // Engagement events
  'section_viewed': {
    section_name: string;
    page: string;
  };
  'testimonial_viewed': {
    testimonial_id?: string;
  };
  'pricing_viewed': {
    pricing_section: string;
  };
}

// Analytics service class
export class AnalyticsService {
  private posthog: any;

  constructor(posthog: any) {
    this.posthog = posthog;
  }

  // Track events with type safety
  track<K extends keyof AnalyticsEvents>(
    event: K,
    properties: AnalyticsEvents[K]
  ): void {
    if (!this.posthog) {
      console.warn('PostHog not initialized');
      return;
    }

    this.posthog.capture(event, {
      ...properties,
      timestamp: new Date().toISOString(),
      user_agent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer,
    });
  }

  // Identify user (for when they provide contact info)
  identify(userId: string, properties?: Record<string, any>): void {
    if (!this.posthog) return;
    
    this.posthog.identify(userId, properties);
  }

  // Set user properties
  setUserProperties(properties: Record<string, any>): void {
    if (!this.posthog) return;
    
    this.posthog.setPersonProperties(properties);
  }

  // Track page views
  trackPageView(pageName: string, additionalProperties?: Record<string, any>): void {
    this.track('page_viewed', {
      page_name: pageName,
      path: window.location.pathname,
      referrer: document.referrer,
      ...additionalProperties,
    });
  }

  // Reset user (for logout or new session)
  reset(): void {
    if (!this.posthog) return;
    
    this.posthog.reset();
  }
}

// Hook to use analytics service
export const useAnalytics = () => {
  const posthog = usePostHog();
  
  const analytics = new AnalyticsService(posthog);
  
  return analytics;
};

// Utility functions for common tracking patterns
export const trackFormStart = (formType: 'booking' | 'contact', source?: string) => {
  const analytics = new AnalyticsService(window.posthog);
  
  if (formType === 'booking') {
    analytics.track('booking_form_started', { source });
  } else {
    analytics.track('contact_form_started', { source });
  }
};

export const trackCTAClick = (ctaText: string, location: string, destination: string) => {
  const analytics = new AnalyticsService(window.posthog);
  analytics.track('cta_clicked', {
    cta_text: ctaText,
    cta_location: location,
    destination,
  });
};

export const trackNavigation = (linkText: string, destination: string, sourcePage: string) => {
  const analytics = new AnalyticsService(window.posthog);
  analytics.track('navigation_clicked', {
    link_text: linkText,
    destination,
    source_page: sourcePage,
  });
};

// Error tracking
export const trackError = (error: Error, context?: string) => {
  const analytics = new AnalyticsService(window.posthog);
  
  if (window.posthog) {
    window.posthog.capture('error_occurred', {
      error_message: error.message,
      error_stack: error.stack,
      context,
      url: window.location.href,
    });
  }
};
