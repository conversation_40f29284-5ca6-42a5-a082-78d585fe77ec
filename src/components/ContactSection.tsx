import React from 'react';
import { Mail, Calendar, ArrowRight } from 'lucide-react';

const ContactSection = () => {
  return (
    <section id="contact" className="py-20 bg-muted overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 rotate-1">
            Get Started
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            Ready to Accelerate Your Marketing?
          </h2>
          <p className="text-xl mt-4 max-w-2xl mx-auto">
            Let's discuss your marketing challenges and create a strategy that drives real results.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Book Consultation Card */}
          <div className="neo-card p-8 bg-primary text-center hover-lift -rotate-1">
            <div className="w-16 h-16 neo-border bg-black text-primary flex items-center justify-center mx-auto mb-6 rotate-12">
              <Calendar size={32} />
            </div>
            <h3 className="text-2xl font-bold mb-4">Book a Strategy Call</h3>
            <p className="mb-6">
              Schedule a 30-minute strategic session to discuss your marketing goals and get actionable insights.
            </p>
            <a 
              href="/booking" 
              className="neo-button bg-black text-white hover:bg-gray-900 inline-flex items-center gap-2"
            >
              Book Strategy Call
              <ArrowRight size={20} />
            </a>
          </div>

          {/* Contact Card */}
          <div className="neo-card p-8 bg-white text-center hover-lift rotate-1">
            <div className="w-16 h-16 neo-border bg-secondary text-white flex items-center justify-center mx-auto mb-6 -rotate-12">
              <Mail size={32} />
            </div>
            <h3 className="text-2xl font-bold mb-4">Send a Message</h3>
            <p className="mb-6">
              Have a specific question or want to discuss a custom project? Drop me a line and I'll get back to you within 24 hours.
            </p>
            <a 
              href="/contact" 
              className="neo-button bg-secondary text-white hover:bg-secondary/90 inline-flex items-center gap-2"
            >
              Contact Me
              <ArrowRight size={20} />
            </a>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">24h</div>
            <p className="text-sm">Response Time</p>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">50+</div>
            <p className="text-sm">Companies Helped</p>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">95%</div>
            <p className="text-sm">Client Satisfaction</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
