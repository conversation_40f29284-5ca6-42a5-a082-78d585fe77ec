import React, { useState } from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { Mail, Phone, MapPin, Clock, Send, CheckCircle } from 'lucide-react';
import { useToast } from '../hooks/use-toast';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      toast({
        title: "Message sent successfully!",
        description: "I'll get back to you within 24 hours.",
      });
      setFormData({ name: '', email: '', company: '', message: '' });
      setIsSubmitting(false);
    }, 1000);
  };

  const contactInfo = [
    {
      icon: <Mail size={24} />,
      label: "Email",
      value: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    {
      icon: <Phone size={24} />,
      label: "Phone",
      value: "+45 93 93 77 96",
      link: "tel:+4593937796"
    },
    {
      icon: <MapPin size={24} />,
      label: "Location",
      value: "In the middle of nowhere, Fuhnen - Denmark",
      link: null
    },
    {
      icon: <Clock size={24} />,
      label: "Response Time",
      value: "Within 24 hours",
      link: null
    }
  ];

  const faqs = [
    {
      question: "What types of businesses do you work with?",
      answer: "I work with startups, scale-ups, and established companies across various industries. My expertise is particularly valuable for B2B SaaS, e-commerce, and tech companies looking to scale their marketing efforts."
    },
    {
      question: "How quickly can we get started?",
      answer: "I typically have availability within 1-2 weeks. For urgent projects, I can often accommodate faster timelines. Book a consultation to discuss your specific needs and timeline."
    },
    {
      question: "Do you work with international clients?",
      answer: "Yes! I work with clients globally. All consultations are conducted remotely via video call, making it easy to collaborate regardless of location."
    },
    {
      question: "What's included in a strategic consultation?",
      answer: "A 60-minute session includes business analysis, strategic recommendations, prioritized action plan, and follow-up resources. You'll also receive a written summary within 48 hours."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-24">
        {/* Hero Section */}
        <section className="py-20 overflow-hidden">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-accent text-white font-bold mb-4 -rotate-1">
                Get In Touch
              </span>
              <h1 className="text-4xl md:text-6xl font-bold leading-tight mb-6">
                Let's Discuss Your <span className="relative inline-block">
                  Marketing Goals
                  <svg className="absolute -bottom-2 left-0 w-full h-3 text-primary" viewBox="0 0 100 8" preserveAspectRatio="none">
                    <path d="M0,5 Q40,0 50,5 Q60,10 100,5" stroke="currentColor" strokeWidth="4" fill="none" />
                  </svg>
                </span>
              </h1>
              <p className="text-xl max-w-3xl mx-auto">
                Ready to accelerate your marketing success? I'm here to help you develop 
                strategies that drive real results. Let's start the conversation.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Form & Info */}
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div>
                <div className="neo-card p-8 bg-white -rotate-1">
                  <h2 className="text-2xl font-bold mb-6">Send me a message</h2>
                  
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <label htmlFor="name" className="block font-bold mb-2">Name *</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="neo-input w-full"
                        placeholder="Your full name"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block font-bold mb-2">Email *</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="neo-input w-full"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="company" className="block font-bold mb-2">Company</label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="neo-input w-full"
                        placeholder="Your company name"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="message" className="block font-bold mb-2">Message *</label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        rows={5}
                        className="neo-input w-full"
                        placeholder="Tell me about your marketing challenges and goals..."
                      />
                    </div>
                    
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="neo-button bg-primary hover:bg-primary/90 text-black w-full flex items-center justify-center gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send size={20} />
                          Send Message
                        </>
                      )}
                    </button>
                  </form>
                </div>
              </div>

              {/* Contact Info */}
              <div>
                <div className="neo-card p-8 bg-white rotate-1">
                  <h2 className="text-2xl font-bold mb-6">Contact Information</h2>
                  
                  <div className="space-y-6 mb-8">
                    {contactInfo.map((info, index) => (
                      <div key={index} className="flex items-start gap-4">
                        <div className="w-12 h-12 neo-border bg-primary flex items-center justify-center rotate-12 flex-shrink-0">
                          {info.icon}
                        </div>
                        <div>
                          <h3 className="font-bold">{info.label}</h3>
                          {info.link ? (
                            <a href={info.link} className="text-secondary hover:underline">
                              {info.value}
                            </a>
                          ) : (
                            <p>{info.value}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="neo-border p-4 bg-primary/10">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      <span className="font-bold">Quick Response Guarantee</span>
                    </div>
                    <p className="text-sm">
                      I personally respond to all inquiries within 24 hours. 
                      For urgent matters, don't hesitate to mention it in your message.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 neo-border bg-secondary text-white font-bold mb-4 rotate-1">
                Common Questions
              </span>
              <h2 className="text-3xl md:text-5xl font-bold">
                Frequently Asked Questions
              </h2>
            </div>
            
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {faqs.map((faq, index) => (
                  <div 
                    key={index}
                    className="neo-card p-6 bg-white hover-lift"
                    style={{ 
                      transform: `rotate(${index % 2 === 0 ? '0.5' : '-0.5'}deg)`
                    }}
                  >
                    <h3 className="font-bold text-lg mb-3">{faq.question}</h3>
                    <p className="text-gray-700">{faq.answer}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="neo-card p-8 md:p-12 bg-primary text-center max-w-2xl mx-auto -rotate-1">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Ready to Get Started?
              </h2>
              <p className="text-xl mb-8">
                Book a strategic consultation and let's discuss how I can help accelerate your marketing success.
              </p>
              <a href="/booking" className="neo-button bg-black text-white hover:bg-gray-900 text-lg px-8 py-4">
                Book Your Consultation
              </a>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Contact;
